import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from threading import Lock
from selenium.common.exceptions import TimeoutException
import time
import os
import re
import random
import logging
import psutil
from datetime import datetime
from queue import Queue, Empty
import hashlib
import threading
import json
from dataclasses import dataclass, field
from typing import List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'ebay_crawler_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

@dataclass(order=True)
class Task:
    """任务类"""
    priority: int
    keyword: str = field(compare=False)
    store_url: str = field(compare=False)
    store_name: str = field(compare=False)
    retry_count: int = field(default=0, compare=False)
    task_id: str = field(default="", compare=False)
    is_end_signal: bool = field(default=False, compare=False)
    worker_id: int = field(default=-1, compare=False)

    def __post_init__(self):
        if not self.task_id and not self.is_end_signal:
            self.task_id = hashlib.md5(f"{self.keyword}_{self.store_url}".encode()).hexdigest()[:8]

class DriverPool:
    """驱动池管理器"""
    def __init__(self, pool_size=5):
        self.pool_size = pool_size
        self.available_drivers = Queue()
        self.all_drivers = []
        self.lock = threading.Lock()
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/120.0.0.0 Safari/537.36",
            # 可以添加更多的 User-Agent
        ]
        self._initialize_pool()

    def _create_driver(self):
        """创建新的driver"""
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        #chrome_options.add_argument("--headless")  # 添加无头模式
        chrome_options.add_argument(f"user-agent={random.choice(self.user_agents)}")

        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(35)
            driver.implicitly_wait(15)
            return driver
        except Exception as e:
            logging.error(f"创建driver失败: {e}")
            raise

    def _initialize_pool(self):
        """初始化驱动池"""
        logging.info(f"初始化驱动池，大小: {self.pool_size}")
        for i in range(self.pool_size):
            try:
                driver = self._create_driver()
                self.all_drivers.append(driver)
                self.available_drivers.put(driver)
                logging.info(f"创建驱动 {i + 1}/{self.pool_size}")
                time.sleep(0.5)
            except Exception as e:
                logging.error(f"创建驱动 {i + 1} 失败: {e}")

    def get_driver(self, timeout=35):
        """获取可用的driver"""
        try:
            driver = self.available_drivers.get(timeout=timeout)
            try:
                _ = driver.current_url  # 检查driver是否有效
                logging.info(f"Driver {driver} 健康检查通过")
                return driver
            except Exception:
                logging.warning(f"Driver {driver} 已失效，创建新的")
                driver.quit()
                return self._create_driver()
        except Empty:
            logging.warning("无可用driver，创建新的")
            return self._create_driver()

    def return_driver(self, driver):
        """归还driver到池中"""
        try:
            if driver and driver in self.all_drivers:
                try:
                    _ = driver.current_url
                    self.available_drivers.put(driver)
                except Exception:
                    logging.warning("Driver失效，不归还到池中")
                    if driver in self.all_drivers:
                        self.all_drivers.remove(driver)
        except Exception as e:
            logging.error(f"归还driver失败: {e}")

    def close_all(self):
        """关闭所有driver"""
        logging.info("关闭所有driver...")
        for driver in self.all_drivers:
            try:
                driver.quit()
            except Exception as e:
                logging.error(f"关闭driver失败: {e}")

class ProgressTracker:
    """进度追踪器（断点续传）"""
    def __init__(self, checkpoint_file="checkpoint.json"):
        self.checkpoint_file = checkpoint_file
        self.completed_tasks = set()
        self.failed_tasks = []
        self.results = []
        self.lock = threading.Lock()
        self.load_checkpoint()

    def load_checkpoint(self):
        """加载检查点"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.completed_tasks = set(data.get('completed', []))
                    self.failed_tasks = data.get('failed', [])
                    self.results = data.get('results', [])
                    logging.info(f"从检查点恢复: 已完成 {len(self.completed_tasks)} 个任务")
            except Exception as e:
                logging.error(f"加载检查点失败: {e}")

    def save_checkpoint(self):
        """保存检查点"""
        with self.lock:
            data = {
                'completed': list(self.completed_tasks),
                'failed': self.failed_tasks,
                'results': self.results
            }
            try:
                with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                logging.error(f"保存检查点失败: {e}")

    def is_completed(self, task_id):
        """检查任务是否已完成"""
        return task_id in self.completed_tasks

    def mark_completed(self, task_id, result):
        """标记任务完成"""
        with self.lock:
            self.completed_tasks.add(task_id)
            self.results.append(result)
            if len(self.completed_tasks) % 10 == 0:
                self.save_checkpoint()

    def mark_failed(self, task_id, error_info):
        """标记任务失败"""
        with self.lock:
            self.failed_tasks.append({
                'task_id': task_id,
                'error': error_info
            })

class OptimizedEbayCrawler:
    """优化的eBay爬虫"""
    def __init__(self, max_workers=5, enable_checkpoint=False):
        self.max_workers = max_workers
        self.driver_pool = DriverPool(pool_size=5)
        self.progress_tracker = ProgressTracker() if enable_checkpoint else None
        self.task_queue = Queue()
        self.stats = {
            'total': 0,
            'completed': 0,
            'failed': 0,
            'skipped': 0
        }
        self.lock = threading.Lock()
        self.stop_signal = threading.Event()
        self.results = []
        self.writer_thread = threading.Thread(target=self.write_results)
        self.writer_thread.start()

        # 启动监控线程
        self.cpu_thread = threading.Thread(target=self.monitor_cpu_usage)
        self.memory_thread = threading.Thread(target=self.monitor_memory_usage)
        self.disk_io_thread = threading.Thread(target=self.monitor_disk_io)
        self.network_io_thread = threading.Thread(target=self.monitor_network_io)

        self.cpu_thread.daemon = True
        self.memory_thread.daemon = True
        self.disk_io_thread.daemon = True
        self.network_io_thread.daemon = True

        self.cpu_thread.start()
        self.memory_thread.start()
        self.disk_io_thread.start()
        self.network_io_thread.start()

    def monitor_cpu_usage(self):
        while not self.stop_signal.is_set():
            cpu_percent = psutil.cpu_percent(interval=1)
            logging.info(f"CPU 使用率: {cpu_percent}%")
            time.sleep(10)

    def monitor_memory_usage(self):
        while not self.stop_signal.is_set():
            memory_info = psutil.virtual_memory()
            logging.info(f"内存使用率: {memory_info.percent}%，已使用: {memory_info.used / (1024 ** 2):.2f} MB，可用: {memory_info.available / (1024 ** 2):.2f} MB")
            time.sleep(10)

    def monitor_disk_io(self):
        while not self.stop_signal.is_set():
            disk_io = psutil.disk_io_counters()
            logging.info(f"磁盘读取次数: {disk_io.read_count}，写入次数: {disk_io.write_count}")
            time.sleep(60)

    def monitor_network_io(self):
        while not self.stop_signal.is_set():
            net_io = psutil.net_io_counters()
            logging.info(f"网络发送字节数: {net_io.bytes_sent / (1024):.2f} KB，接收字节数: {net_io.bytes_recv / (1024):.2f} KB")
            time.sleep(60)

    def parse_store_url(self, url):
        """解析店铺名称"""
        patterns = [
            r'_ssn=([^&]+)',
            r'/usr/([^/?\s]+)',
            r'/sch/([^/?\s]+)/m.html',
            r'/seller/([^/?\s]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return "unknown_store"

    def scroll_page(self, driver: webdriver.Chrome, min_scrolls=4, max_scrolls=12):
        """全局随机滚动页面"""
        scroll_times = random.randint(min_scrolls, max_scrolls)
        for _ in range(scroll_times):
            scroll_height = random.randint(300, 1000)
            driver.execute_script(f"window.scrollBy(0, {scroll_height});")
            time.sleep(random.uniform(5, 20))

    def fetch_image_links(self, driver: WebDriver) -> List[str]:
        """提取商品页面的图片链接"""
        image_urls = []
        seen = set()

        try:
            if "itm" not in driver.current_url:
                logging.error(f"当前页面不是商品页面，URL: {driver.current_url}")
                return image_urls

            logging.info(f"当前页面 URL: {driver.current_url}")

            driver.implicitly_wait(200)

            WebDriverWait(driver, 200).until(
                EC.visibility_of_element_located((By.XPATH, "//*[@id='PicturePanel']"))
            )
            logging.info("图片面板加载成功，开始提取图片链接")
            time.sleep(random.uniform(3, 10))

            selectors = [
                "//*[@id='PicturePanel']//div[contains(@class, 'ux-image-carousel-item')]/img"
            ]

            for sel in selectors:
                try:
                    elements = driver.find_elements(By.XPATH, sel)
                    logging.info(f"找到的元素数量: {len(elements)}")
                    for elem in elements:
                        url = elem.get_attribute("data-zoom-src") or elem.get_attribute("src")
                        if url and url not in seen:
                            image_urls.append(url)
                            seen.add(url)
                            logging.info(f"提取到图片: {url}")

                except Exception as e:
                    logging.warning(f"使用选择器 {sel} 时出错: {e}")

            time.sleep(random.uniform(5, 20))

        except TimeoutException:
            logging.error("加载超时，未能成功加载图片面板。")
        except Exception as e:
            logging.error(f"获取图片链接失败: {e}")

        return image_urls

    def write_results(self):
        """写入结果到 Excel 文件"""
        output_file = r"D:\IdeaProjects\ebay picturecatch\results.xlsx"

        from openpyxl import Workbook
        workbook = Workbook()
        sheet = workbook.active
        sheet.append(["Task ID", "Store Name", "Store URL", "Keyword", "Product Link", "Product Title",
                      "Image URLs", "Status", "Error"])
        counter = 0
        write_frequency = 5

        while not self.stop_signal.is_set() or self.results:
            try:
                if self.results:
                    logging.info("尝试获取写入锁...")
                    with self.lock:
                        logging.info("成功获取写入锁，开始写入...")
                        result = self.results.pop(0)
                        logging.info(f"写入结果到Excel: {result}")
                        sheet.append([result["task_id"], result["store_name"], result["store_url"],
                                      result["keyword"], result["product_link"], result["product_title"],
                                      result["image_urls"], result["status"], result["error"]])
                        counter += 1

                        if counter >= write_frequency:
                            workbook.save(output_file)
                            logging.info(f"已保存 {write_frequency} 条结果到 Excel 文件")
                            counter = 0

                    logging.info("释放写入锁...")
                time.sleep(1)
            except Exception as e:
                logging.error(f"写入结果失败: {e}")

    def crawl_single_item(self, driver, task: Task) -> dict:
        """爬取单个任务并处理重试机制"""
        result = {
            "task_id": task.task_id,
            "store_name": task.store_name,
            "store_url": task.store_url,
            "keyword": task.keyword,
            "product_link": "",
            "product_title": "",
            "image_urls": "",
            "status": "pending",
            "error": "",
            "timestamp": datetime.now().isoformat(),
            "results_count": 0
        }

        max_retries = 2
        retries = 0

        while retries < max_retries:
            try:
                logging.info("清除所有 Cookie")
                driver.delete_all_cookies()

                logging.info(f"访问店铺页面: {task.store_url}")
                driver.get(task.store_url)
                time.sleep(random.uniform(7, 25))

                new_user_agent = random.choice(self.driver_pool.user_agents)
                logging.info(f"轮换 User-Agent: {new_user_agent}")
                driver.execute_cdp_cmd('Network.setUserAgentOverride', {"userAgent": new_user_agent})

                logging.info("调用全局滚动方法")
                self.scroll_page(driver)
                logging.info("成功访问店铺页面")

                search_box = None
                search_selectors = [
                    "//div[contains(@class, 's-answer-region-center-top')]//form[contains(@class, 'str-search')]//input[@class='str-search__input']",
                    "//div[contains(@class,'s-answer-region-center-top')]//form[contains(@class,'str-search')]//input[contains(@class,'str-search__input')]",
                    "//*[@id='mainContent']//input[@placeholder='Search all items']",
                    "//*[@id='mainContent']//input[@name='_nkw']"
                ]

                logging.info(f"任务 {task.task_id}: 查找搜索框开始")
                for selector in search_selectors:
                    try:
                        logging.info(f"任务 {task.task_id}: 查找搜索框: {selector}")
                        search_box = WebDriverWait(driver, 30).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                        logging.info(f"任务 {task.task_id}: 找到搜索框: {selector}")
                        break
                    except TimeoutException:
                        logging.warning(f"任务 {task.task_id}: 未找到搜索框: {selector}")
                        continue
                logging.info(f"任务 {task.task_id}: 查找搜索框结束")
                if not search_box:
                    result["status"] = "failed"
                    result["error"] = "找不到搜索框"
                    logging.error("找不到搜索框，任务失败")
                    self.results.append(result)
                    return result

                logging.info(f"任务 {task.task_id}: 清空搜索框并输入关键词: {task.keyword} 开始")
                search_box.clear()
                time.sleep(random.uniform(5, 25))
                search_box.send_keys(task.keyword)
                time.sleep(random.uniform(5, 25))
                logging.info(f"任务 {task.task_id}: 清空搜索框并输入关键词: {task.keyword} 结束")

                logging.info(f"任务 {task.task_id}: 提交搜索 开始")
                search_box.submit()
                time.sleep(random.uniform(5, 25))
                logging.info(f"任务 {task.task_id}: 提交搜索 结束")

                logging.info(f"任务 {task.task_id}: 等待搜索结果加载 开始")
                WebDriverWait(driver, 120).until(
                    EC.presence_of_element_located((By.XPATH, "//ul[@class='srp-results srp-list clearfix']"))
                )
                logging.info(f"任务 {task.task_id}: 等待搜索结果加载 结束")

                results_count_element = WebDriverWait(driver, 80).until(
                    EC.presence_of_element_located((By.XPATH, "//h1[contains(@class, 'srp-controls__count-heading')]"))
                )
                results_count_text = results_count_element.text
                logging.info(f"结果文本: {results_count_text}")

                match = re.search(r'(\d+)\s+results?', results_count_text)
                results_count = int(match.group(1)) if match else 0
                result["results_count"] = results_count
                logging.info(f"总结果数: {results_count}")

                if results_count == 0:
                    result["status"] = "failed"
                    result["error"] = "未找到匹配的商品"
                    logging.warning("未找到匹配的商品")
                    self.results.append(result)
                    return result
                time.sleep(random.uniform(5, 20))
                # 点击每个有效商品链接
                items = driver.find_elements(By.XPATH, "//ul[@class='srp-results srp-list clearfix']/li[contains(@class, 's-card--horizontal')]")
                max_items = min(results_count, len(items))

                for index in range(max_items):
                    item = items[index]
                    try:
                        logging.info("点击产品链接...")
                        link = item.find_element(By.XPATH, ".//a[contains(@class,'s-link') or contains(@href,'/itm/')]")
                        action = ActionChains(driver)
                        action.move_to_element(link).click().perform()  # 使用 ActionChains 进行点击
                        time.sleep(random.uniform(5, 20))

                        # 切换到新标签页并提取图片链接
                        driver.switch_to.window(driver.window_handles[-1])  # 切换到最后打开的窗口
                        time.sleep(random.uniform(7, 25))
                        self.scroll_page(driver)
                        product_link = driver.current_url  # 获取产品链接
                        product_title = driver.title  # 获取产品标题
                        result["product_link"] = product_link
                        result["product_title"] = product_title
                        image_urls = self.fetch_image_links(driver)
                        result["image_urls"] = ",".join(image_urls)  # 将图片链接格式化
                        result["status"] = "success"  # 更新状态

                        # 保存结果到列表中
                        self.results.append(result.copy())  # 确保保存每个任务的结果
                        logging.info(f"结果已保存: {result['task_id']}，内容: {result}")

                        # 返回到搜索结果标签页
                        driver.close()  # 关闭当前标签页
                        driver.switch_to.window(driver.window_handles[0])  # 切换回搜索结果页
                        time.sleep(random.uniform(5, 25))  # 等待返回
                        logging.info("成功返回搜索结果页面")
                        time.sleep(random.uniform(5, 15))
                    except Exception as e:
                        logging.error(f"处理商品链接失败: {e}，商品链接: {item.get_attribute('outerHTML')}")
                        result["status"] = "failed"
                        result["error"] = str(e)
                        self.results.append(result.copy())  # 确保保存结果
                        logging.info(f"结果已保存: {result['task_id']}，内容: {result}")
                        break  # 跳出当前商品循环，进入下一个商品

                return result  # 成功完成任务后返回结果

            except Exception as e:
                logging.error(f"爬取单个任务错误: {e}")
                result["status"] = "failed"
                result["error"] = str(e)
                self.results.append(result.copy())  # 确保保存结果
                logging.info(f"结果已保存: {result['task_id']}，内容: {result}")

                retries += 1  # 增加重试次数
                logging.warning(f"重试第 {retries}/{max_retries} 次...")

                # 如果重试达到最大次数，则重启驱动
                if retries >= max_retries:
                    logging.warning("达到最大重试次数，重启驱动...")
                    self.driver_pool.return_driver(driver)  # 归还失效的驱动
                    driver = self.driver_pool.get_driver()  # 获取新的驱动
                    time.sleep(5)  # 等待一段时间再重试

        # 如果所有重试都失败，则返回结果
        result["status"] = "failed"
        result["error"] = "任务失败，已重试多次"
        self.results.append(result.copy())
        return result

    def worker(self, worker_id):
        """工作线程"""
        driver = None
        consecutive_failures = 0
        max_consecutive_failures = 5

        try:
            while not self.stop_signal.is_set():
                try:
                    task = self.task_queue.get(timeout=1)  # 添加超时机制
                    logging.info(f"[Worker-{worker_id}] 获取任务: {task.task_id if not task.is_end_signal else '结束信号'}, 关键词: {task.keyword}, 店铺: {task.store_url}")
                    logging.info(f"[Worker-{worker_id}] 开始处理任务: {task.task_id}, Driver: {driver}")

                    # 检查是否是结束信号
                    if task.is_end_signal:
                        logging.info(f"[Worker-{worker_id}] 收到结束信号")
                        self.task_queue.put(task)  # 将结束信号放回队列
                        break

                        # 检查进度追踪器
                    if self.progress_tracker and self.progress_tracker.is_completed(task.task_id):
                        with self.lock:
                            self.stats['skipped'] += 1
                        continue

                    if driver is None:
                        driver = self.driver_pool.get_driver()

                    logging.info(f"[Worker-{worker_id}] 处理: {task.keyword} @ {task.store_name}")

                    result = self.crawl_single_item(driver, task)
                    logging.info(f"[Worker-{worker_id}] 结果: {result}")  # 添加日志输出

                    logging.info("尝试获取结果列表锁...")
                    with self.lock:
                        logging.info("成功获取结果列表锁，开始更新结果...")
                        self.results.append(result)  # 直接将结果添加到共享列表
                        if result["status"] == "success":
                            self.stats['completed'] += 1
                            consecutive_failures = 0
                            logging.info(f"[Worker-{worker_id}] 成功: {task.task_id} ({self.stats['completed']}/{self.stats['total']})")
                        else:
                            self.stats['failed'] += 1
                            consecutive_failures += 1
                            logging.warning(f"[Worker-{worker_id}] 失败: {task.task_id} - {result['error']}")
                    logging.info("释放结果列表锁...")

                    if self.progress_tracker:
                        if result["status"] == "success":
                            self.progress_tracker.mark_completed(task.task_id, result)
                        else:
                            self.progress_tracker.mark_failed(task.task_id, result["error"])

                            # 如果连续失败次数达到上限，则重新创建driver
                    if consecutive_failures >= max_consecutive_failures:
                        logging.warning(f"[Worker-{worker_id}] 连续失败{consecutive_failures}次，重新创建 driver")
                        self.driver_pool.return_driver(driver)
                        driver = None
                        consecutive_failures = 0

                except Empty:
                    logging.info(f"[Worker-{worker_id}] 任务队列为空")
                    continue
                except Exception as e:
                    logging.error(f"[Worker-{worker_id}] 错误: {e}")
                    consecutive_failures += 1
                    time.sleep(1)

        except Exception as e:
            logging.error(f"[Worker-{worker_id}] 全局异常: {e}")
        finally:
            if driver:
                self.driver_pool.return_driver(driver)
            logging.info(f"[Worker-{worker_id}] 结束")

    def process_batch(self, keywords: List[str], store_urls: List[str]) -> pd.DataFrame:
        """批量处理"""
        tasks = []

        for store_url in store_urls:
            store_name = self.parse_store_url(store_url)
            for keyword in keywords:
                task = Task(
                    priority=0,
                    task_id="",
                    store_name=store_name,
                    store_url=store_url,
                    keyword=keyword
                )

                task.worker_id = len(tasks) % self.max_workers

                logging.info(f"任务创建: {task.task_id}, 关键词: {task.keyword}, 店铺: {task.store_url}")
                if self.progress_tracker and self.progress_tracker.is_completed(task.task_id):
                    self.stats['skipped'] += 1
                    for result in self.progress_tracker.results:
                        if result.get('task_id') == task.task_id:
                            self.results.append(result)  # 被跳过的结果直接添加到结果列表
                            break
                else:
                    tasks.append(task)

        self.stats['total'] = len(keywords) * len(store_urls)
        logging.info(f"总任务数: {self.stats['total']} ({len(store_urls)} 店铺 × {len(keywords)} 关键词)")

        if not tasks:
            logging.info("所有任务已完成，无需处理")
            return pd.DataFrame(self.results)

        logging.info("开始将任务放入任务队列...")
        for task in tasks:
            logging.info(f"任务入队: {task.task_id}, 关键词: {task.keyword}, 店铺: {task.store_url}")
            self.task_queue.put(task)
            logging.info(f"任务放入队列: {task.task_id}")
        logging.info("所有任务放入任务队列完成")

        threads = []
        for i in range(self.max_workers):
            t = threading.Thread(target=self.worker, args=(i,), name=f"Worker-{i}")
            t.start()
            threads.append(t)

        start_time = time.time()

        while True:
            time.sleep(10)

            with self.lock:
                processed = self.stats['completed'] + self.stats['failed'] + self.stats['skipped']

            if processed >= self.stats['total']:
                break

            elapsed = time.time() - start_time
            speed = processed / (elapsed / 60) if elapsed > 0 else 0
            remaining = (self.stats['total'] - processed) / speed if speed > 0 else 0

            current_progress_log = (
                f"进度: {processed}/{self.stats['total']} "
                f"({processed / self.stats['total'] * 100:.1f}%) | "
                f"速度: {speed:.1f} 个/分钟 | "
                f"预计剩余: {remaining:.1f} 分钟"
                f"任务队列大小: {self.task_queue.qsize()}"
            )
            logging.info(current_progress_log)

            if self.task_queue.empty() and processed >= self.stats['total'] - len(threads):
                break

        for _ in range(self.max_workers):
            end_task = Task(
                priority=999999,
                keyword="",
                store_url="",
                store_name="",
                is_end_signal=True
            )
            self.task_queue.put(end_task)

        for t in threads:
            t.join(timeout=30)

        if self.progress_tracker:
            self.progress_tracker.save_checkpoint()
        logging.info(f"处理完毕，结果数量: {len(self.results)}")
        return pd.DataFrame(self.results)

    def cleanup(self):
        """清理资源"""
        self.stop_signal.set()
        self.writer_thread.join()
        self.driver_pool.close_all()

def main():
    """主函数"""
    # excel_path = r"D:\IdeaProjects\ebay picturecatch\input.xlsx"
    # output_dir = r"D:\IdeaProjects\ebay picturecatch"
    excel_path = "input.xlsx"
    output_dir = "/Users/<USER>/Desktop/REACH/Scripts/eBay"

    if not os.path.exists(excel_path):
        logging.error(f"文件不存在: {excel_path}")
        return

    try:
        logging.info("读取输入文件...")
        df_keywords = pd.read_excel(excel_path, sheet_name=0)
        keywords = df_keywords.iloc[:, 1].dropna().tolist()

        try:
            df_stores = pd.read_excel(excel_path, sheet_name=1)
            store_urls = df_stores.iloc[:, 0].dropna().tolist()
        except Exception as e:
            logging.warning("未找到店铺链接，使用默认值")
            store_urls = []

        if not keywords:
            logging.error("没有找到关键词")
            return

        enable_checkpoint = True
        checkpoint_file = "checkpoint.json"
        if os.path.exists(checkpoint_file):
            response = input("发现上次未完成的任务，是否继续？(y/n): ")
            if response.lower() != 'y':
                os.remove(checkpoint_file)
                logging.info("已删除检查点文件，重新开始")

        max_workers = 5
        logging.info(f"使用 {max_workers} 个工作线程")

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        crawler = OptimizedEbayCrawler(
            max_workers=max_workers,
            enable_checkpoint=enable_checkpoint
        )

        start_time = time.time()
        results_df = crawler.process_batch(keywords, store_urls)
        elapsed_time = time.time() - start_time

        logging.info("保存结果...")

        if results_df.empty:
            logging.error("没有结果可以保存")
            return

        stats_data = {
            '统计项': ['总任务数', '成功数', '失败数', '无结果数', '跳过数', '总耗时(分钟)', '平均速度(个/分钟)'],
            '数量': [
                len(results_df),
                len(results_df[results_df['status'] == 'success']),
                len(results_df[results_df['status'].isin(['failed', 'timeout'])]),
                len(results_df[results_df['status'] == 'no_results']),
                crawler.stats.get('skipped', 0),
                f"{elapsed_time / 60:.2f}",
                f"{len(results_df) / (elapsed_time / 60):.2f}" if elapsed_time > 0 else "N/A"
            ]
        }
        stats_df = pd.DataFrame(stats_data)

        with pd.ExcelWriter(os.path.join(output_dir, f"final_statistics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"), engine='openpyxl') as writer:
            results_df.to_excel(writer, sheet_name='All_Results', index=False)
            stats_df.to_excel(writer, sheet_name='Statistics', index=False)

        logging.info("-- 爬取完成！ --")
        logging.info(f"最终结果已保存到: {output_dir}")

    except Exception as e:
        logging.error(f"主程序错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'crawler' in locals():
            crawler.cleanup()

if __name__ == "__main__":
    main()